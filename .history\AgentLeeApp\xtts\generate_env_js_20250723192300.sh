#!/bin/bash
# Generate browser-safe env.js from .env for <PERSON> frontend
set -e
ENV_JS_PATH="/app/env.js"
ENV_FILE=".env"

# Start env.js
echo "window.env = {" > "$ENV_JS_PATH"

SAFE_KEYS=(
  agent_lee_public_url
  MCP_INDEXEDDB_NAMESPACE
  MCP_TASK_DB
  MCP_STATUS_DB
  MCP_LOG_DB
  FIREBASE_API_KEY
  FIREBASE_DATABASE_URL
  FIREBASE_STORAGE_BUCKET
  DUCKDUCKGO_SEARCH_API_KEY
  GOOGLE_API_KEY
  OPENROUTER_API_KEY
  EMAILJS_SERVICE_ID
  EMAILJS_TEMPLATE_ID
  EMAILJS_PUBLIC_KEY
  OpenWeather_API_KEY
  Exa_Team_ID
  Resend_API_URL
)

for key in "${SAFE_KEYS[@]}"; do
  value=$(grep -E "^$key=" "$ENV_FILE" | cut -d'=' -f2-)
  if [ -n "$value" ]; then
    echo "  $key: '$value'," >> "$ENV_JS_PATH"
  fi
done

# End env.js
echo "};" >> "$ENV_JS_PATH"
