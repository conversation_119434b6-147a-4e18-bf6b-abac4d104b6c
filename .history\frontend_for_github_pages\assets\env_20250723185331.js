// Auto-generated browser-safe environment config
window.AGENT_LEE_ENV = {
  GOOGLE_API_KEY: "AIzaSyBpJiw1ym8tTPGI4Mi6a00qSPiudtgoDXo",
  Exa_API_KEY: "b0b2bcd9-59fd-41cc-aad4-72cc353bd215",
  FIREBASE_API_KEY: "AIzaSyDneLA9SPpbvlFMl9QkKhyhIqlbo-hZSHA",
  OPENROUTER_API_KEY: "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",
  DUCKDUCKGO_SEARCH_API_KEY: "23de90506707108ad5600e4949611d2a1e568cb8be470ba600517eb150e0ba67",
  EMAILJS_PUBLIC_KEY: "X0xz2e39-ULZc-I47",
  EMAILJS_SERVICE_ID: "service_wgaybsm",
  EMAILJS_TEMPLATE_ID: "template_xxxx",
  OpenWeather_API_KEY: "********************************",
  TELEGRAM_BOT_TOKEN: "**********************************************",
  ALLOWED_USERS: "Leonard",
  PERSONAL_TELEGRAM_CHAT_ID: "6939665945",
  PERSONAL_EMAIL: "<EMAIL>",
  LEONARD_PHONE_NUMBER: "+14143038580",
  NGROK_AUTH_TOKEN: "*************************************************",
  agent_lee_public_url: "https://4bc3-2603-6000-b400-5739-fd70-f099-5dba-8e7d.ngrok-free.app",
  Resend_API_KEY: "re_evNnvdcV_HNBwVGvjqv9qSyq8Z68rEp44",
  Resend_API_URL: "https://api.resend.com",
  Exa_Team_ID: "Team ID cmdgbry2j0022jhz5dx47qywj",
  ADMIN_EMAIL: "<EMAIL>",
  Fly_io_token: "********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************HM6Ly9hcGkuZmx5LmlvL2FhYS92MZgEks5ogV8RzwAAAAEkeX0vF84AD/cMCpHOAA/3DAzEEFUPwVoBM+J8T4hQggwmNjvEINbne74Fr7ONawQZgtAU8d1rSMbR6hT0jMtbovGAnEwc",
  AGENT_LEE_ID: "LEE_MCP_001",
  AGENT_LEE_ROLE: "orchestrator",
  AGENTLEE_MCP_MODE: "TRUE",
  AGENTLEE_MCP_VERSION: "1.0.0",
  AGENTLEE_MCP_NAME: "Agent Lee",
  DOCKER_USERNAME: "4citeb4u",
  DOCKER_ACCESS_TOKEN: "************************************",
  DOCKER_IMAGE_NAME: "agentlee/mcp",
  DOCKER_IMAGE_TAG: "latest",
  LANGCHAIN_API_KEY: "ls__8572cbfc1c054fa08445b460bb69c974",
  LANGCHAIN_TRACING_V2: "false",
  LANGCHAIN_PROJECT: "CleanCoderAI",
  LANGCHAIN_ENDPOINT: "https://api.smith.langchain.com",
  MCP_INDEXEDDB_NAMESPACE: "agentlee_mcp",
  MCP_TASK_DB: "agentlee_tasks",
  MCP_STATUS_DB: "agentlee_status",
  MCP_LOG_DB: "agentlee_logs",
  FIREBASE_DATABASE_URL: "https://console.firebase.google.com/project/watson2024/firestore/databases/-default-/data/~2F",
  FIREBASE_STORAGE_BUCKET: "watson2024.appspot.com",
  GOOGLE_CLIENT_ID: "249742914141-d8hlmrc02pd7gmcgopev4gaiuhi8st3e.apps.googleusercontent.com",
  GOOGLE_CLIENT_SECRET: "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",
  GOOGLE_REFRESH_TOKEN: "1//05GbfjueMQuc4CgYIARAAGAUSNwF-L9IrEp_cf24TAyhrW6BYl1KRXJ5565J9HqVJQzWhfQnRDK4oNfDpYT0miPfPl5C5DsJaUj0",
  Model: "google/gemma-3n-e2b-it:free",
  EMAIL_USER: "<EMAIL>",
  EMAIL_APP_PASSWORD: "czmg xzpz qipz efjv",
  EMAILJS_PRIVATE_KEY: "-ZZ_QHFN4pgSLn-rDUCRs",
  // Add more keys as needed
};
