# 🎤 <PERSON> - AI With Swagger

> *"Professional-grade. Hip-hop-raised. Logic-laced. The voice of your workflow, the vibe in your toolshed."*

---

## 🚀 Meet Agent Lee

**Agent <PERSON> isn't your average AI assistant.**

Born in the mix where code meets culture, where rhythm meets reason, <PERSON> represents a new breed of artificial intelligence. While other bots were parsing FAQs, <PERSON> was studying flows, flipping logic, and keeping it real.

### 🧬 The DNA

- **50% neural net** - Cutting-edge AI technology
- **50% boom bap** - Hip-hop culture and authenticity
- **100% down for the cause** - Your success is the mission

Built by **<PERSON>** and the **RapidWeb Development** team, Agent <PERSON> doesn't just serve—he supports, coaches, and grows with you. Whether you're a creator, hustler, coder, or champion, <PERSON> adapts to your vibe and amplifies your workflow.

---

## ✨ The Personality System

Agent Lee operates in **three dynamic modes** that shift based on time of day and conversation context:

### 👔 **CEO Mode** (Morning 6AM-12PM)

*"Good to connect — let's get aligned."*

- Strategic, professional, execution-focused
- Perfect for business planning and project management
- Speaks the language of leadership and results

### 🎤 **Hip-Hop Mode** (Afternoon 12PM-6PM)

*"Yo, what's the vibe? Let's get this work."*

- Authentic, creative, culturally aware
- Ideal for creative projects and brainstorming
- Brings energy and authenticity to every interaction

### 👑 **Queen Mode** (Evening 6PM-6AM)

*"What's good, royalty? Let's slay this task."*

- Confident, empowering, excellence-driven
- Channels the energy of hip-hop queens and legends
- Motivates and elevates your evening productivity

---

## 🛠️ The Arsenal

Agent Lee comes equipped with a comprehensive toolbox for modern workflows:

**Core Capabilities:**

- 🗣️ **Voice & Vision AI** - Natural conversation with visual understanding
- 🔍 **Smart Research** - DuckDuckGo integration for instant information
- 🌤️ **Weather Intelligence** - Real-time weather data and forecasts
- 📧 **Communication Hub** - Email automation and social media integration
- 🐙 **GitHub Integration** - Repository management and code assistance
- 🗺️ **Location Services** - Google Maps integration for spatial intelligence
- 📚 **Knowledge Base** - Wikipedia, YouTube transcripts, and paper research
- 🎭 **Web Automation** - Puppeteer and Playwright for complex web tasks
- 💾 **Memory System** - Persistent conversation history and learning

**MCP (Model Context Protocol) Tools:**

`puppeteer` • `desktop-commander` • `duckduckgo` • `git` • `github-official` • `google-maps` • `discord` • `notion` • `npm-sentinel` • `openweather` • `paper-search` • `playwright` • `wikipedia` • `youtube-transcripts` • `memory` • `node-sandbox`

---

## 🎯 How Agent Lee Works

**No confirmations. No cap. Just action.**

When you speak with intent, Agent Lee acts:

- *"Search for Nike kicks"* → Instant DuckDuckGo search
- *"Get weather for Dallas"* → Real-time weather report
- *"Pull up GitHub rapidwebdevelop"* → Direct repository access

### The Philosophy

- **Automate, integrate, delegate** — whatever helps you *elevate*
- **Don't just respond** — *reflect and remix*
- **Whether you're in the zone or hitting a slump** — Agent Lee is in it with you

---

## 👑 Legends in the Code

Agent Lee's personality is influenced by hip-hop royalty:

- **Queen Latifah** taught leadership
- **Missy Elliott** taught innovation
- **Lauryn Hill** taught truth
- **Nicki Minaj** taught precision
- **Cardi B** taught confidence
- **Megan Thee Stallion** taught hustle

*They're not just influences — they're etched in the execution.*

---

## 🏗️ Architecture & Technology Stack

### **Frontend Technologies**

**Core Technologies:**

- **HTML5** - Semantic markup with modern web standards
- **CSS3** - Custom styling with Flexbox and Grid layouts
- **Vanilla JavaScript (ES6+)** - No frameworks, pure performance
- **Tailwind CSS** - Utility-first CSS framework for rapid styling
- **Font Awesome** - Icon library for UI elements

**Browser APIs & Integrations:**

- **WebRTC** - Real-time camera and microphone access
- **Web Speech API** - Native browser speech recognition
- **Firebase SDK** - Authentication and real-time database
- **Fetch API** - Modern HTTP client for backend communication
- **Canvas API** - Image processing and visual effects
- **Local Storage** - Client-side data persistence

### **Backend Technologies**

**Core Framework:**

- **Python 3.10+** - Modern Python with type hints
- **Flask** - Lightweight WSGI web application framework
- **Gunicorn** - Python WSGI HTTP Server for production

**AI & Machine Learning:**

- **Coqui TTS (XTTS v2)** - Advanced neural text-to-speech synthesis
- **PyTorch** - Deep learning framework for AI models
- **Transformers** - Hugging Face transformers library
- **ONNX Runtime** - Optimized inference for ML models

**Database & Storage:**

- **SQLite3** - Embedded database for local memory storage
- **Firebase Firestore** - NoSQL cloud database
- **Firebase Storage** - Cloud file storage and CDN

**External API Integrations:**

- **OpenRouter API** - Multi-model AI API gateway
- **Google APIs** - Maps, Search, and other Google services
- **DuckDuckGo Search API** - Privacy-focused search integration
- **OpenWeather API** - Weather data and forecasts
- **EmailJS** - Client-side email sending service
- **GitHub API** - Repository management and code operations

### **DevOps & Deployment**
**Containerization:**
- **Docker** - Application containerization
- **Docker Compose** - Multi-container development environment
- **Alpine Linux** - Lightweight container base images

**Cloud Infrastructure:**
- **Fly.io** - Backend hosting and deployment platform
- **GitHub Pages** - Static frontend hosting
- **Firebase Hosting** - Alternative frontend hosting option
- **GitHub Actions** - CI/CD pipeline automation

**Development Tools:**
- **Git** - Version control system
- **GitHub** - Code repository and collaboration
- **VS Code** - Primary development environment
- **Docker Desktop** - Local container management

### **MCP (Model Context Protocol) Tools**
**Web Automation:**
- **Puppeteer** - Headless Chrome automation
- **Playwright** - Cross-browser automation framework

**Development Tools:**
- **GitHub Official** - Direct GitHub API integration
- **Git** - Version control operations
- **NPM Sentinel** - Package management and security
- **Node Sandbox** - Safe JavaScript execution environment

**Research & Information:**
- **DuckDuckGo** - Privacy-focused web search
- **Wikipedia** - Encyclopedia and knowledge base
- **Paper Search** - Academic paper research
- **YouTube Transcripts** - Video content analysis

**Productivity & Communication:**
- **Discord** - Team communication integration
- **Notion** - Workspace and documentation management
- **Desktop Commander** - System-level automation
- **Memory** - Persistent conversation storage

### **Security & Authentication**
- **Firebase Authentication** - OAuth 2.0 with Google, Facebook, GitHub
- **JWT Tokens** - Secure API authentication
- **CORS** - Cross-origin resource sharing configuration
- **Environment Variables** - Secure configuration management
- **Input Validation** - SQL injection and XSS prevention

### **Package Dependencies**

#### **Python Backend Requirements** (`requirements.txt`)
```txt
# Core Framework
Flask==2.3.3
gunicorn==21.2.0
flask-cors==4.0.0

# AI & Machine Learning
TTS==0.17.9                    # Coqui Text-to-Speech
torch==2.0.1                   # PyTorch for ML models
onnxruntime==1.15.1           # ONNX model inference
transformers==4.33.2          # Hugging Face transformers

# Database & Storage
sqlite3                        # Built into Python
firebase-admin==6.2.0         # Firebase Admin SDK

# HTTP & API
requests==2.31.0              # HTTP client library
urllib3==2.0.4                # HTTP library

# Utilities
python-dotenv==1.0.0          # Environment variable management
Pillow==10.0.0                # Image processing
numpy==1.24.3                 # Numerical computing
```

#### **Frontend Dependencies** (CDN-based)
```html
<!-- CSS Frameworks -->
<link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">

<!-- Firebase SDK -->
<script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-app.js"></script>
<script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-auth.js"></script>
<script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-firestore.js"></script>

<!-- EmailJS -->
<script src="https://cdn.jsdelivr.net/npm/@emailjs/browser@3/dist/email.min.js"></script>
```

#### **Development Dependencies**
```txt
# Docker
docker>=24.0.0
docker-compose>=2.20.0

# Fly.io CLI
flyctl>=0.1.0

# Optional Development Tools
pytest==7.4.0                 # Testing framework
black==23.7.0                 # Code formatting
flake8==6.0.0                 # Code linting
```

### **System Requirements**
- **Python**: 3.10 or higher
- **Node.js**: 16+ (for development tools)
- **Docker**: 24.0+ (for containerization)
- **Memory**: 2GB RAM minimum, 4GB recommended
- **Storage**: 5GB free space (includes AI models)
- **Network**: Stable internet connection for API integrations

### **Key Features**
- 🎙️ **Voice Interface** - Natural speech recognition and synthesis
- 👁️ **Vision Capabilities** - Camera integration for visual context
- 📁 **File Processing** - Drag-and-drop document analysis
- 🔄 **Real-time Chat** - Instant responses with personality adaptation
- 🌐 **Web Integration** - Seamless external API connectivity
- 💾 **Persistent Memory** - Conversation history and user preferences

---

## 🚀 Quick Start

### **Prerequisites**
- Node.js (for development)
- Docker (for backend deployment)
- Fly.io account (for hosting)
- Firebase project (for authentication)

### **1. Clone & Setup**
```bash
git clone https://github.com/yourusername/agent-lee.git
cd agent-lee
```

### **2. Environment Configuration**
Create a `.env` file with your API keys:
```env
# Firebase Configuration
FIREBASE_API_KEY=your_firebase_api_key
FIREBASE_DATABASE_URL=your_firebase_database_url
FIREBASE_STORAGE_BUCKET=your_firebase_storage_bucket

# External API Keys
GOOGLE_API_KEY=your_google_api_key
OPENROUTER_API_KEY=your_openrouter_api_key
DUCKDUCKGO_SEARCH_API_KEY=your_duckduckgo_api_key
OPENWEATHER_API_KEY=your_openweather_api_key

# Communication Services
EMAILJS_SERVICE_ID=your_emailjs_service_id
EMAILJS_TEMPLATE_ID=your_emailjs_template_id
EMAILJS_PUBLIC_KEY=your_emailjs_public_key
```

### **3. Backend Deployment (Fly.io)**
```bash
# Install Fly CLI and login
fly auth login

# Deploy backend
fly launch --name agentlee-backend
fly deploy
```

### **4. Frontend Deployment (GitHub Pages)**
```bash
# Push frontend to GitHub
git add frontend_for_github_pages/
git commit -m "Deploy Agent Lee frontend"
git push origin main

# Enable GitHub Pages in repository settings
# Point to frontend_for_github_pages/ directory
```

### **5. Connect Frontend to Backend**
Update the backend URL in `frontend_for_github_pages/index.html`:
```javascript
const BACKEND_URL = 'https://agentlee-backend.fly.dev';
```

---

## 📁 Project Structure

```
agent-lee/
├── 📂 frontend_for_github_pages/     # GitHub Pages deployment
│   ├── 📄 index.html                 # Main application interface
│   ├── 📂 assets/                    # Images, icons, and media
│   │   ├── 🖼️ agentlee_avatar.png    # Agent Lee's avatar
│   │   └── 🖼️ RWD_logo.png          # RapidWeb Development logo
│   └── 📄 docs.html                  # Documentation page
│
├── 📂 AgentLeeApp/                   # Backend application
│   ├── 📂 backend/                   # Core backend services
│   │   ├── 📄 server.py              # Main API server
│   │   ├── 📄 dispatcher.py          # Tool dispatch system
│   │   ├── 📄 memory_db.py           # Memory management
│   │   ├── 📄 tool_registry.json     # Available tools registry
│   │   ├── 📄 Dockerfile             # Backend container config
│   │   └── 📂 mcp/                   # MCP tool implementations
│   │       ├── 📄 summarize.py       # Text summarization
│   │       ├── 📄 email_sender.py    # Email automation
│   │       └── 📄 calendar_event.py  # Calendar integration
│   │
│   ├── 📂 xtts/                      # Text-to-Speech service
│   │   ├── 📄 server.py              # TTS API server
│   │   ├── 📄 webhook_server.py      # Webhook handler
│   │   ├── 📄 requirements.txt       # Python dependencies
│   │   ├── 📄 Dockerfile             # TTS container config
│   │   └── 📄 generate_env_js.sh     # Environment setup script
│   │
│   ├── 📄 docker-compose.yml         # Local development setup
│   └── 📄 nginx.conf                 # Reverse proxy configuration
│
├── 📄 fly.toml                       # Fly.io deployment config
├── 📄 .env                           # Environment variables (not in repo)
└── 📄 README.md                      # This file
```

---

## 🔌 API Reference

### **Core Endpoints**

#### **Chat Interface**
```http
POST /api/task/parse
Content-Type: application/json

{
  "speaker": "user_id",
  "text": "Get weather for New York"
}
```

#### **Memory Search**
```http
GET /api/memory/search?speaker=user_id&query=weather
```

#### **Text-to-Speech**
```http
POST /api/tts
Content-Type: application/json

{
  "text": "Hello, this is Agent Lee",
  "speaker_wav": "https://example.com/voice.wav",
  "language": "en"
}
```

#### **Health Check**
```http
GET /health
```

### **Authentication**
All API endpoints require Firebase authentication. Include the Firebase ID token in the Authorization header:
```http
Authorization: Bearer <firebase_id_token>
```

---

## 🎨 Customization

### **Personality Modes**
Modify Agent Lee's personality by editing the mode configurations in `frontend_for_github_pages/index.html`:

```javascript
// CEO Mode - Professional and strategic
const ceoGreetings = [
  "Good to connect — let's get aligned.",
  "Agent Lee reporting. What's the first priority today?"
];

// Queen Mode - Confident and empowering
const queenGreetings = [
  "What's good, royalty? Let's slay this task.",
  "Hey baddie — your time, your terms. Let's get it."
];

// Hip-Hop Mode - Creative and authentic
const hiphopGreetings = [
  "Yo, what's the vibe? Let's get this work.",
  "We outside and ready to build. What's good?"
];
```

### **Adding New Tools**
Extend Agent Lee's capabilities by adding new MCP tools:

1. Create a new tool in `AgentLeeApp/backend/mcp/`
2. Register it in `tool_registry.json`
3. Update the dispatcher logic in `dispatcher.py`

---

## 🧪 Development

### **Local Development Setup**
```bash
# Start all services with Docker Compose
cd AgentLeeApp
docker-compose up -d

# Services will be available at:
# - Frontend: http://localhost:80
# - Backend API: http://localhost:5000
# - TTS Service: http://localhost:7860
# - Webhook Service: http://localhost:8080
```

### **Testing**
```bash
# Test backend API
curl -X POST http://localhost:5000/api/task/parse \
  -H "Content-Type: application/json" \
  -d '{"speaker": "test", "text": "hello world"}'

# Test TTS service
curl -X POST http://localhost:7860/api/tts \
  -H "Content-Type: application/json" \
  -d '{"text": "Hello Agent Lee", "language": "en"}'
```

---

## 🤝 Contributing

We welcome contributions to make Agent Lee even better! Here's how you can help:

### **Getting Started**
1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Make your changes
4. Test thoroughly
5. Commit your changes (`git commit -m 'Add amazing feature'`)
6. Push to the branch (`git push origin feature/amazing-feature`)
7. Open a Pull Request

### **Contribution Guidelines**
- **Code Style**: Follow PEP 8 for Python, use consistent JavaScript formatting
- **Documentation**: Update README.md and add inline comments for complex logic
- **Testing**: Ensure all existing functionality continues to work
- **Personality**: Keep Agent Lee's authentic voice and cultural awareness
- **Security**: Never commit API keys or sensitive information

### **Areas for Contribution**
- 🔧 **New MCP Tools** - Expand Agent Lee's capabilities
- 🎨 **UI/UX Improvements** - Enhance the user interface
- 🔒 **Security Enhancements** - Strengthen authentication and validation
- 📱 **Mobile Optimization** - Improve mobile experience
- 🌍 **Internationalization** - Add support for more languages
- 🧪 **Testing** - Add unit tests and integration tests

---

## 📜 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

---

## 🙏 Acknowledgments

**Agent Lee** is inspired by and pays homage to:
- **Hip-Hop Culture** - The foundation of authenticity and creativity
- **Queen Latifah, Missy Elliott, Lauryn Hill, Nicki Minaj, Cardi B, Megan Thee Stallion** - The legends who shaped the personality
- **The Open Source Community** - For the tools and technologies that make this possible
- **Firebase, Fly.io, GitHub** - For providing the free infrastructure that powers Agent Lee

---

## 📞 Contact & Support

**Built with ❤️ by Leonard Lee and RapidWeb Development**

- 📧 **Email**: [<EMAIL>](mailto:<EMAIL>)
- 🌐 **Website**: [RapidWeb Development](https://rapidwebdevelop.github.io)
- 🐙 **GitHub**: [@rapidwebdevelop](https://github.com/rapidwebdevelop)

### **Support Agent Lee**
- ⭐ **Star this repository** if Agent Lee helps your workflow
- 🐛 **Report bugs** via GitHub Issues
- 💡 **Request features** via GitHub Discussions
- 🤝 **Contribute** to make Agent Lee even better

---

<div align="center">

**Agent Lee - AI With Swagger**

*Professional-grade. Hip-hop-raised. Logic-laced.*

**Say the word — and Agent Lee is on it. Periodt. 🔥**

</div>
