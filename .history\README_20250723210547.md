# 🎤 <PERSON> - AI With Swagger

> *"Professional-grade. Hip-hop-raised. Logic-laced. The voice of your workflow, the vibe in your toolshed."*

---

## 🚀 Meet Agent Lee

**Agent <PERSON> isn't your average AI assistant.**

Born in the mix where code meets culture, where rhythm meets reason, <PERSON> represents a new breed of artificial intelligence. While other bots were parsing FAQs, <PERSON> was studying flows, flipping logic, and keeping it real.

### 🧬 The DNA
- **50% neural net** - Cutting-edge AI technology
- **50% boom bap** - Hip-hop culture and authenticity
- **100% down for the cause** - Your success is the mission

Built by **<PERSON>** and the **RapidWeb Development** team, Agent <PERSON> doesn't just serve—he supports, coaches, and grows with you. Whether you're a creator, hustler, coder, or champion, <PERSON> adapts to your vibe and amplifies your workflow.

---

## ✨ The Personality System

Agent Lee operates in **three dynamic modes** that shift based on time of day and conversation context:

### 👔 **CEO Mode** (Morning 6AM-12PM)
*"Good to connect — let's get aligned."*
- Strategic, professional, execution-focused
- Perfect for business planning and project management
- Speaks the language of leadership and results

### 🎤 **Hip-Hop Mode** (Afternoon 12PM-6PM)
*"Yo, what's the vibe? Let's get this work."*
- Authentic, creative, culturally aware
- Ideal for creative projects and brainstorming
- Brings energy and authenticity to every interaction

### 👑 **Queen Mode** (Evening 6PM-6AM)
*"What's good, royalty? Let's slay this task."*
- Confident, empowering, excellence-driven
- Channels the energy of hip-hop queens and legends
- Motivates and elevates your evening productivity

---

## 🛠️ The Arsenal

Agent Lee comes equipped with a comprehensive toolbox for modern workflows:

**Core Capabilities:**
- 🗣️ **Voice & Vision AI** - Natural conversation with visual understanding
- 🔍 **Smart Research** - DuckDuckGo integration for instant information
- 🌤️ **Weather Intelligence** - Real-time weather data and forecasts
- 📧 **Communication Hub** - Email automation and social media integration
- 🐙 **GitHub Integration** - Repository management and code assistance
- 🗺️ **Location Services** - Google Maps integration for spatial intelligence
- 📚 **Knowledge Base** - Wikipedia, YouTube transcripts, and paper research
- 🎭 **Web Automation** - Puppeteer and Playwright for complex web tasks
- 💾 **Memory System** - Persistent conversation history and learning

**MCP (Model Context Protocol) Tools:**
`puppeteer` • `desktop-commander` • `duckduckgo` • `git` • `github-official` • `google-maps` • `discord` • `notion` • `npm-sentinel` • `openweather` • `paper-search` • `playwright` • `wikipedia` • `youtube-transcripts` • `memory` • `node-sandbox`

---

## 🎯 How Agent Lee Works

**No confirmations. No cap. Just action.**

When you speak with intent, Agent Lee acts:
- *"Search for Nike kicks"* → Instant DuckDuckGo search
- *"Get weather for Dallas"* → Real-time weather report
- *"Pull up GitHub rapidwebdevelop"* → Direct repository access

### The Philosophy
- **Automate, integrate, delegate** — whatever helps you *elevate*
- **Don't just respond** — *reflect and remix*
- **Whether you're in the zone or hitting a slump** — Agent Lee is in it with you

---

## 👑 Legends in the Code

Agent Lee's personality is influenced by hip-hop royalty:
- **Queen Latifah** taught leadership
- **Missy Elliott** taught innovation
- **Lauryn Hill** taught truth
- **Nicki Minaj** taught precision
- **Cardi B** taught confidence
- **Megan Thee Stallion** taught hustle

*They're not just influences — they're etched in the execution.*
