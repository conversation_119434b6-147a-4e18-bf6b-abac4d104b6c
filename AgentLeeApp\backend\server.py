from flask import Flask, request, jsonify
from memory_db import store_memory, search_memory
from dispatcher import run_tool
import os

app = Flask(__name__)

@app.route('/api/task/parse', methods=['POST'])
def parse():
    data = request.json
    speaker = data.get('speaker')
    text = data.get('text')
    result = run_tool(text)
    store_memory(speaker, text, result)
    return jsonify({'response': result})

@app.route('/api/memory/search', methods=['GET'])
def memory_search():
    speaker = request.args.get('speaker')
    query = request.args.get('query')
    results = search_memory(speaker, query)
    return jsonify({'results': results})

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=int(os.getenv('PORT', 5000)))
