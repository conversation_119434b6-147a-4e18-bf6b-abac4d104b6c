import json
import subprocess

def run_tool(input_text):
    with open('/app/tool_registry.json') as f:
        registry = json.load(f)
    for tool in registry:
        if tool['trigger'] in input_text.lower():
            exec_path = tool['path']
            result = subprocess.run(['python', exec_path, input_text], capture_output=True)
            return result.stdout.decode() or result.stderr.decode()
    return "No matching tool found."
