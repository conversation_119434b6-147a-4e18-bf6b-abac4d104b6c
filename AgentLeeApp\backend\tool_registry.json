[{"name": "summarizer", "trigger": "summarize", "description": "Summarizes long text or transcripts", "path": "/app/mcp/summarize.py"}, {"name": "email_sender", "trigger": "send email", "description": "Sends formatted emails using EmailJS or SMTP", "path": "/app/mcp/email_sender.py"}, {"name": "calendar_event", "trigger": "create calendar event", "description": "Creates events via calendar integration", "path": "/app/mcp/calendar_event.py"}]