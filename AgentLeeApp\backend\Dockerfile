# Base Python image
FROM python:3.10-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    sqlite3 \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
RUN pip install --no-cache-dir flask

# Create data directory
RUN mkdir -p /app/data

# Copy project files
COPY . .

# Expose port
EXPOSE 5000

# Start the backend server
CMD ["python", "server.py"]
