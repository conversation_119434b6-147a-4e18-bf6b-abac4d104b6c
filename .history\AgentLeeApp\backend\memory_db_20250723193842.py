import sqlite3

def store_memory(speaker, text, result):
    conn = sqlite3.connect("/app/data/memory.db")
    c = conn.cursor()
    c.execute('CREATE TABLE IF NOT EXISTS memory (speaker TEXT, text TEXT, result TEXT)')
    c.execute('INSERT INTO memory VALUES (?, ?, ?)', (speaker, text, result))
    conn.commit()
    conn.close()

def search_memory(speaker=None, query=None):
    conn = sqlite3.connect("/app/data/memory.db")
    c = conn.cursor()
    sql = 'SELECT speaker, text, result FROM memory WHERE 1=1'
    params = []
    if speaker:
        sql += ' AND speaker=?'
        params.append(speaker)
    if query:
        sql += ' AND text LIKE ?'
        params.append(f'%{query}%')
    c.execute(sql, params)
    results = c.fetchall()
    conn.close()
    return results
