

services:
  nginx:
    image: nginx:alpine
    container_name: nginx_auth
    ports:
      - "80:80"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/htpasswd:/etc/nginx/.htpasswd:ro
      - ./assets:/usr/share/nginx/html/assets
      - ./index.html:/usr/share/nginx/html/index.html
    depends_on:
      - backend
    restart: unless-stopped

  xtts:
    build: ./xtts
    container_name: xtts_server
    env_file:
      - ../.env
    ports:
      - "7860:7860"
    volumes:
      - ./xtts:/app
    command: ["bash", "-c", "cd /app && ./generate_env_js.sh && python server.py"]
    restart: unless-stopped

  backend:
    build: ./xtts
    container_name: backend_server
    env_file:
      - ../.env
    command: ["python", "server.py"]
    ports:
      - "5000:5000"
    depends_on:
      - xtts
    restart: unless-stopped

  webhook:
    build: ./xtts
    container_name: webhook_server
    env_file:
      - ../.env
    environment:
      - XTTS_API=http://xtts:7860/api/tts
    command: gunicorn -w 2 -b 0.0.0.0:8080 webhook_server:app
    ports:
      - "8080:8080"
    depends_on:
      - xtts
    restart: unless-stopped
