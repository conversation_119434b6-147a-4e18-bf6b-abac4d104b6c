

services:
  nginx:
    image: nginx:alpine
    container_name: nginx_auth
    ports:
      - "80:80"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/htpasswd:/etc/nginx/.htpasswd:ro
      - ./assets:/usr/share/nginx/html/assets
      - ./index.html:/usr/share/nginx/html/index.html
    depends_on:
      - backend
    restart: unless-stopped

  xtts:
    build: ./xtts
    container_name: xtts_server
    env_file:
      - ../.env
    environment:
      # MCP/Agent Lee environment variables (auto-loaded from .env)
      - AGENT_LEE_ID=${AGENT_LEE_ID}
      - AGENT_LEE_ROLE=${AGENT_LEE_ROLE}
      - AGENTLEE_MCP_MODE=${AGENTLEE_MCP_MODE}
      - AGENTLEE_MCP_VERSION=${AGENTLEE_MCP_VERSION}
      - AGENTLEE_MCP_NAME=${AGENTLEE_MCP_NAME}
      - DOCKER_USERNAME=${DOCKER_USERNAME}
      - DOCKER_ACCESS_TOKEN=${DOCKER_ACCESS_TOKEN}
      - DOCKER_IMAGE_NAME=${DOCKER_IMAGE_NAME}
      - DOCKER_IMAGE_TAG=${DOCKER_IMAGE_TAG}
      - LANGCHAIN_API_KEY=${LANGCHAIN_API_KEY}
      - LANGCHAIN_TRACING_V2=${LANGCHAIN_TRACING_V2}
      - LANGCHAIN_PROJECT=${LANGCHAIN_PROJECT}
      - LANGCHAIN_ENDPOINT=${LANGCHAIN_ENDPOINT}
      - MCP_INDEXEDDB_NAMESPACE=${MCP_INDEXEDDB_NAMESPACE}
      - MCP_TASK_DB=${MCP_TASK_DB}
      - MCP_STATUS_DB=${MCP_STATUS_DB}
      - MCP_LOG_DB=${MCP_LOG_DB}
      - FIREBASE_API_KEY=${FIREBASE_API_KEY}
      - FIREBASE_DATABASE_URL=${FIREBASE_DATABASE_URL}
      - FIREBASE_STORAGE_BUCKET=${FIREBASE_STORAGE_BUCKET}
      - DUCKDUCKGO_SEARCH_API_KEY=${DUCKDUCKGO_SEARCH_API_KEY}
      - GOOGLE_API_KEY=${GOOGLE_API_KEY}
      - GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID}
      - GOOGLE_CLIENT_SECRET=${GOOGLE_CLIENT_SECRET}
      - GOOGLE_REFRESH_TOKEN=${GOOGLE_REFRESH_TOKEN}
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
      - Model=${Model}
      - EMAIL_USER=${EMAIL_USER}
      - EMAIL_APP_PASSWORD=${EMAIL_APP_PASSWORD}
      - TELEGRAM_BOT_TOKEN=${TELEGRAM_BOT_TOKEN}
      - ALLOWED_USERS=${ALLOWED_USERS}
      - PERSONAL_TELEGRAM_CHAT_ID=${PERSONAL_TELEGRAM_CHAT_ID}
      - PERSONAL_EMAIL=${PERSONAL_EMAIL}
      - LEONARD_PHONE_NUMBER=${LEONARD_PHONE_NUMBER}
      - NGROK_AUTH_TOKEN=${NGROK_AUTH_TOKEN}
      - agent_lee_public_url=${agent_lee_public_url}
      - Resend_API_KEY=${Resend_API_KEY}
      - Resend_API_URL=${Resend_API_URL}
      - Exa_API_KEY=${Exa_API_KEY}
      - Exa_Team_ID=${Exa_Team_ID}
      - ADMIN_EMAIL=${ADMIN_EMAIL}
      - EMAILJS_SERVICE_ID=${EMAILJS_SERVICE_ID}
      - EMAILJS_TEMPLATE_ID=${EMAILJS_TEMPLATE_ID}
      - EMAILJS_PUBLIC_KEY=${EMAILJS_PUBLIC_KEY}
      - EMAILJS_PRIVATE_KEY=${EMAILJS_PRIVATE_KEY}
      - OpenWeather_API_KEY=${OpenWeather_API_KEY}
      - Fly.io_token=${Fly.io_token}
    ports:
      - "7860:7860"
    volumes:
      - ./xtts:/app
    command: ["bash", "-c", "cd /app && ./generate_env_js.sh && python server.py"]
    restart: unless-stopped

  backend:
    build: ./xtts
    container_name: backend_server
    env_file:
      - ../.env
    environment:
      # MCP/Agent Lee environment variables (auto-loaded from .env)
      - AGENT_LEE_ID=${AGENT_LEE_ID}
      - AGENT_LEE_ROLE=${AGENT_LEE_ROLE}
      - AGENTLEE_MCP_MODE=${AGENTLEE_MCP_MODE}
      - AGENTLEE_MCP_VERSION=${AGENTLEE_MCP_VERSION}
      - AGENTLEE_MCP_NAME=${AGENTLEE_MCP_NAME}
      - DOCKER_USERNAME=${DOCKER_USERNAME}
      - DOCKER_ACCESS_TOKEN=${DOCKER_ACCESS_TOKEN}
      - DOCKER_IMAGE_NAME=${DOCKER_IMAGE_NAME}
      - DOCKER_IMAGE_TAG=${DOCKER_IMAGE_TAG}
      - LANGCHAIN_API_KEY=${LANGCHAIN_API_KEY}
      - LANGCHAIN_TRACING_V2=${LANGCHAIN_TRACING_V2}
      - LANGCHAIN_PROJECT=${LANGCHAIN_PROJECT}
      - LANGCHAIN_ENDPOINT=${LANGCHAIN_ENDPOINT}
      - MCP_INDEXEDDB_NAMESPACE=${MCP_INDEXEDDB_NAMESPACE}
      - MCP_TASK_DB=${MCP_TASK_DB}
      - MCP_STATUS_DB=${MCP_STATUS_DB}
      - MCP_LOG_DB=${MCP_LOG_DB}
      - FIREBASE_API_KEY=${FIREBASE_API_KEY}
      - FIREBASE_DATABASE_URL=${FIREBASE_DATABASE_URL}
      - FIREBASE_STORAGE_BUCKET=${FIREBASE_STORAGE_BUCKET}
      - DUCKDUCKGO_SEARCH_API_KEY=${DUCKDUCKGO_SEARCH_API_KEY}
      - GOOGLE_API_KEY=${GOOGLE_API_KEY}
      - GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID}
      - GOOGLE_CLIENT_SECRET=${GOOGLE_CLIENT_SECRET}
      - GOOGLE_REFRESH_TOKEN=${GOOGLE_REFRESH_TOKEN}
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
      - Model=${Model}
      - EMAIL_USER=${EMAIL_USER}
      - EMAIL_APP_PASSWORD=${EMAIL_APP_PASSWORD}
      - TELEGRAM_BOT_TOKEN=${TELEGRAM_BOT_TOKEN}
      - ALLOWED_USERS=${ALLOWED_USERS}
      - PERSONAL_TELEGRAM_CHAT_ID=${PERSONAL_TELEGRAM_CHAT_ID}
      - PERSONAL_EMAIL=${PERSONAL_EMAIL}
      - LEONARD_PHONE_NUMBER=${LEONARD_PHONE_NUMBER}
      - NGROK_AUTH_TOKEN=${NGROK_AUTH_TOKEN}
      - agent_lee_public_url=${agent_lee_public_url}
      - Resend_API_KEY=${Resend_API_KEY}
      - Resend_API_URL=${Resend_API_URL}
      - Exa_API_KEY=${Exa_API_KEY}
      - Exa_Team_ID=${Exa_Team_ID}
      - ADMIN_EMAIL=${ADMIN_EMAIL}
      - EMAILJS_SERVICE_ID=${EMAILJS_SERVICE_ID}
      - EMAILJS_TEMPLATE_ID=${EMAILJS_TEMPLATE_ID}
      - EMAILJS_PUBLIC_KEY=${EMAILJS_PUBLIC_KEY}
      - EMAILJS_PRIVATE_KEY=${EMAILJS_PRIVATE_KEY}
      - OpenWeather_API_KEY=${OpenWeather_API_KEY}
      - Fly.io_token=${Fly.io_token}
    command: ["python", "server.py"]
    ports:
      - "5000:5000"
    depends_on:
      - xtts
    restart: unless-stopped

  webhook:
    build: ./xtts
    container_name: webhook_server
    env_file:
      - ../.env
    environment:
      # MCP/Agent Lee environment variables (auto-loaded from .env)
      - XTTS_API=http://xtts:7860/api/tts
      - AGENT_LEE_ID=${AGENT_LEE_ID}
      - AGENT_LEE_ROLE=${AGENT_LEE_ROLE}
      - AGENTLEE_MCP_MODE=${AGENTLEE_MCP_MODE}
      - AGENTLEE_MCP_VERSION=${AGENTLEE_MCP_VERSION}
      - AGENTLEE_MCP_NAME=${AGENTLEE_MCP_NAME}
      - DOCKER_USERNAME=${DOCKER_USERNAME}
      - DOCKER_ACCESS_TOKEN=${DOCKER_ACCESS_TOKEN}
      - DOCKER_IMAGE_NAME=${DOCKER_IMAGE_NAME}
      - DOCKER_IMAGE_TAG=${DOCKER_IMAGE_TAG}
      - LANGCHAIN_API_KEY=${LANGCHAIN_API_KEY}
      - LANGCHAIN_TRACING_V2=${LANGCHAIN_TRACING_V2}
      - LANGCHAIN_PROJECT=${LANGCHAIN_PROJECT}
      - LANGCHAIN_ENDPOINT=${LANGCHAIN_ENDPOINT}
      - MCP_INDEXEDDB_NAMESPACE=${MCP_INDEXEDDB_NAMESPACE}
      - MCP_TASK_DB=${MCP_TASK_DB}
      - MCP_STATUS_DB=${MCP_STATUS_DB}
      - MCP_LOG_DB=${MCP_LOG_DB}
      - FIREBASE_API_KEY=${FIREBASE_API_KEY}
      - FIREBASE_DATABASE_URL=${FIREBASE_DATABASE_URL}
      - FIREBASE_STORAGE_BUCKET=${FIREBASE_STORAGE_BUCKET}
      - DUCKDUCKGO_SEARCH_API_KEY=${DUCKDUCKGO_SEARCH_API_KEY}
      - GOOGLE_API_KEY=${GOOGLE_API_KEY}
      - GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID}
      - GOOGLE_CLIENT_SECRET=${GOOGLE_CLIENT_SECRET}
      - GOOGLE_REFRESH_TOKEN=${GOOGLE_REFRESH_TOKEN}
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
      - Model=${Model}
      - EMAIL_USER=${EMAIL_USER}
      - EMAIL_APP_PASSWORD=${EMAIL_APP_PASSWORD}
      - TELEGRAM_BOT_TOKEN=${TELEGRAM_BOT_TOKEN}
      - ALLOWED_USERS=${ALLOWED_USERS}
      - PERSONAL_TELEGRAM_CHAT_ID=${PERSONAL_TELEGRAM_CHAT_ID}
      - PERSONAL_EMAIL=${PERSONAL_EMAIL}
      - LEONARD_PHONE_NUMBER=${LEONARD_PHONE_NUMBER}
      - NGROK_AUTH_TOKEN=${NGROK_AUTH_TOKEN}
      - agent_lee_public_url=${agent_lee_public_url}
      - Resend_API_KEY=${Resend_API_KEY}
      - Resend_API_URL=${Resend_API_URL}
      - Exa_API_KEY=${Exa_API_KEY}
      - Exa_Team_ID=${Exa_Team_ID}
      - ADMIN_EMAIL=${ADMIN_EMAIL}
      - EMAILJS_SERVICE_ID=${EMAILJS_SERVICE_ID}
      - EMAILJS_TEMPLATE_ID=${EMAILJS_TEMPLATE_ID}
      - EMAILJS_PUBLIC_KEY=${EMAILJS_PUBLIC_KEY}
      - EMAILJS_PRIVATE_KEY=${EMAILJS_PRIVATE_KEY}
      - OpenWeather_API_KEY=${OpenWeather_API_KEY}
      - Fly.io_token=${Fly.io_token}
    command: gunicorn -w 2 -b 0.0.0.0:8080 webhook_server:app
    ports:
      - "8080:8080"
    depends_on:
      - xtts
    restart: unless-stopped
