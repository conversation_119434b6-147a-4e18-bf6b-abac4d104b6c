Metadata-Version: 2.4
Name: bnnumerizer
Version: 0.0.2
Summary: Bangla Number text to String Converter
Home-page: https://github.com/banglakit/number-to-bengali-word
Author: <PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON>
Author-email: 
License: MIT
Keywords: bangla,number,word numerizer
Classifier: Development Status :: 3 - Alpha
Classifier: Intended Audience :: Education
Classifier: Operating System :: OS Independent
Classifier: License :: OSI Approved :: MIT License
Classifier: Programming Language :: Python :: 3
Description-Content-Type: text/markdown
License-File: LICENSE
Dynamic: author
Dynamic: classifier
Dynamic: description
Dynamic: description-content-type
Dynamic: home-page
Dynamic: keywords
Dynamic: license
Dynamic: license-file
Dynamic: summary

# bnNumerizer
Bangla number to Bangla string converter based on the work [banglakit/number-to-bengali-word](https://github.com/banglakit/number-to-bengali-word)

```
original authors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON>
pypi: mnansary
```

# install
```python
pip install bnnumerizer
```
# useage
```python
from bnnumerizer import numerize
text= "বিল গেটসের ব্যাংক ব্যালেন্স ২২২১২৩৪৫৬.১২৩৪ টাকা। এর মধ্যে আমার অর্থের পরিমাণ ৩৪৫ টাকা মাত্র "
numerize(text)
```
```
'বিল গেটসের ব্যাংক ব্যালেন্স বাইশ কোটি একুশ লক্ষ তেইশ হাজার চার শত ছাপ্পান্ন দশমিক এক দুই তিন চার টাকা। এর মধ্যে আমার অর্থের পরিমাণ তিন শত পঁয়তাল্লিশ টাকা মাত্র '
```
# Limitations
* can not convert numbers > "৯৯৯৯৯৯৯৯৯" 

# Citing
* if you find the work useful please star/ cite the **Orininal Repository**: https://github.com/banglakit/number-to-bengali-word


Change Log
===========
