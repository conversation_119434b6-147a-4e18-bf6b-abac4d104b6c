version: '3.8'

services:
  # Backend service for task parsing and memory management
  backend:
    build: ./backend
    container_name: agentlee_backend
    ports:
      - "5000:5000"
    volumes:
      - ./backend:/app
      - ./backend/data:/app/data
      - ./backend/mcp:/app/mcp
      - ./backend/tool_registry.json:/app/tool_registry.json
    env_file:
      - ../.env
    restart: unless-stopped

  # NGINX reverse proxy with authentication
  nginx:
    image: nginx:alpine
    container_name: nginx_auth
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/htpasswd:/etc/nginx/.htpasswd:ro
      - ./assets:/usr/share/nginx/html/assets
      - ./index.html:/usr/share/nginx/html/index.html
    depends_on:
      - backend
    restart: unless-stopped

  # XTTS Text-to-Speech service
  xtts:
    build: ./xtts
    container_name: xtts_server
    env_file:
      - ../.env
    environment:
      # Only safe/public keys for frontend exposure
      - agent_lee_public_url=${agent_lee_public_url}
      - MCP_INDEXEDDB_NAMESPACE=${MCP_INDEXEDDB_NAMESPACE}
      - MCP_TASK_DB=${MCP_TASK_DB}
      - MCP_STATUS_DB=${MCP_STATUS_DB}
      - MCP_LOG_DB=${MCP_LOG_DB}
      - FIREBASE_API_KEY=${FIREBASE_API_KEY}
      - FIREBASE_DATABASE_URL=${FIREBASE_DATABASE_URL}
      - FIREBASE_STORAGE_BUCKET=${FIREBASE_STORAGE_BUCKET}
      - DUCKDUCKGO_SEARCH_API_KEY=${DUCKDUCKGO_SEARCH_API_KEY}
      - GOOGLE_API_KEY=${GOOGLE_API_KEY}
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
      - EMAILJS_SERVICE_ID=${EMAILJS_SERVICE_ID}
      - EMAILJS_TEMPLATE_ID=${EMAILJS_TEMPLATE_ID}
      - EMAILJS_PUBLIC_KEY=${EMAILJS_PUBLIC_KEY}
      - OpenWeather_API_KEY=${OpenWeather_API_KEY}
      - Exa_Team_ID=${Exa_Team_ID}
      - Resend_API_URL=${Resend_API_URL}
    ports:
      - "7860:7860"
    volumes:
      - ./xtts:/app
    command: ["bash", "-c", "cd /app && ./generate_env_js.sh && python server.py"]
    restart: unless-stopped

  # Webhook service for handling external webhooks
  webhook:
    build: ./xtts
    container_name: webhook_server
    env_file:
      - ../.env
    environment:
      - XTTS_API=http://xtts:7860/api/tts
      # Only safe/public keys for frontend exposure
      - agent_lee_public_url=${agent_lee_public_url}
      - MCP_INDEXEDDB_NAMESPACE=${MCP_INDEXEDDB_NAMESPACE}
      - MCP_TASK_DB=${MCP_TASK_DB}
      - MCP_STATUS_DB=${MCP_STATUS_DB}
      - MCP_LOG_DB=${MCP_LOG_DB}
      - FIREBASE_API_KEY=${FIREBASE_API_KEY}
      - FIREBASE_DATABASE_URL=${FIREBASE_DATABASE_URL}
      - FIREBASE_STORAGE_BUCKET=${FIREBASE_STORAGE_BUCKET}
      - DUCKDUCKGO_SEARCH_API_KEY=${DUCKDUCKGO_SEARCH_API_KEY}
      - GOOGLE_API_KEY=${GOOGLE_API_KEY}
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
      - EMAILJS_SERVICE_ID=${EMAILJS_SERVICE_ID}
      - EMAILJS_TEMPLATE_ID=${EMAILJS_TEMPLATE_ID}
      - EMAILJS_PUBLIC_KEY=${EMAILJS_PUBLIC_KEY}
      - OpenWeather_API_KEY=${OpenWeather_API_KEY}
      - Exa_Team_ID=${Exa_Team_ID}
      - Resend_API_URL=${Resend_API_URL}
    command: ["gunicorn", "-w", "2", "-b", "0.0.0.0:8080", "webhook_server:app"]
    ports:
      - "8080:8080"
    depends_on:
      - xtts
    restart: unless-stopped
